import React from 'react'

interface CardProps {
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'elevated' | 'glass' | 'premium'
  hover?: boolean
}

export const Card: React.FC<CardProps> = ({ children, className = '', variant = 'elevated', hover = false }) => {
  const baseClasses = `
    bg-white border border-slate-200 rounded-2xl
    transition-all duration-300 ease-in-out
    ${hover ? 'hover:shadow-xl hover:scale-[1.02] cursor-pointer' : ''}
  `.trim()

  const variantClasses = {
    default: 'shadow-md',
    elevated: 'shadow-xl shadow-slate-900/10',
    glass: 'glass-morphism backdrop-blur-lg',
    premium: `
      shadow-2xl shadow-slate-900/15
      bg-white border-slate-200
      ring-1 ring-slate-100
    `.trim()
  }

  return <div className={`${baseClasses} ${variantClasses[variant]} ${className}`}>{children}</div>
}

interface CardHeaderProps {
  children: React.ReactNode
  className?: string
  gradient?: boolean
}

export const CardHeader: React.FC<CardHeaderProps> = ({ children, className = '', gradient = false }) => {
  const baseClasses = `
    px-8 py-6 border-b border-slate-100
    ${gradient ? 'bg-gradient-to-r from-slate-50 to-green-50' : ''}
  `.trim()

  return <div className={`${baseClasses} ${className}`}>{children}</div>
}

interface CardContentProps {
  children: React.ReactNode
  className?: string
  padding?: 'sm' | 'md' | 'lg' | 'xl'
}

export const CardContent: React.FC<CardContentProps> = ({ children, className = '', padding = 'lg' }) => {
  const paddingClasses = {
    sm: 'px-4 py-3',
    md: 'px-6 py-4',
    lg: 'px-8 py-6',
    xl: 'px-10 py-8'
  }

  return <div className={`${paddingClasses[padding]} ${className}`}>{children}</div>
}

interface CardFooterProps {
  children: React.ReactNode
  className?: string
  gradient?: boolean
}

export const CardFooter: React.FC<CardFooterProps> = ({ children, className = '', gradient = false }) => {
  const baseClasses = `
    px-8 py-6 border-t border-slate-100
    ${gradient ? 'bg-gradient-to-r from-green-50 to-slate-50' : ''}
  `.trim()

  return <div className={`${baseClasses} ${className}`}>{children}</div>
}

// New enhanced card components for auth
interface AuthCardProps {
  children: React.ReactNode
  className?: string
  title?: string
  subtitle?: string
}

export const AuthCard: React.FC<AuthCardProps> = ({ children, className = '', title, subtitle }) => {
  return (
    <Card variant="premium" className={`animate-scale-in ${className}`}>
      {(title || subtitle) && (
        <CardHeader gradient>
          {title && <h2 className="text-2xl font-bold text-gray-900 text-center">{title}</h2>}
          {subtitle && <p className="text-gray-600 text-center mt-2">{subtitle}</p>}
        </CardHeader>
      )}
      <CardContent padding="xl">{children}</CardContent>
    </Card>
  )
}
